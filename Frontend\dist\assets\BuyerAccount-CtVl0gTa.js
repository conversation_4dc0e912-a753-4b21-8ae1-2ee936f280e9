import{d as w,e as o,r as c,U as S,f as M,j as e,V as v,x as V,W as O,X as W,Y as G,Z as K,$ as Q,u as X,K as Z,a0 as P,a1 as z,a2 as H,a3 as J,a4 as ee,a5 as ae,a6 as se,a7 as le,a8 as te,a9 as re,aa as g,ab as ie,ac as de,ad as I,ae as q,af as ce,ag as f,ah as ne,o as oe,ai as ue,aj as x}from"./index-CbuIrTWR.js";import{B as me}from"./BuyerAccount-CrcfuMXo.js";import{S as y,B as E}from"./BuyerAccountDashboard-_eZc4Wqm.js";import{t as C}from"./toast-5PAXudRI.js";import{T as D}from"./Table-CPAiTSiF.js";const he=()=>{const a=w(),{user:r,isLoading:i,isSuccess:s,isError:t,error:u}=o(m=>m.auth),[d,p]=c.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:""}),[k,b]=c.useState(!0),[j,l]=c.useState(null),[A,R]=c.useState(null);c.useEffect(()=>{a(S())},[a]),c.useEffect(()=>{r&&(p({firstName:r.firstName||"",lastName:r.lastName||"",email:r.email||"",mobile:r.mobile||"",profileImage:r.profileImage||""}),B(!1),b(!1))},[r]);const[N,_]=c.useState(!1),[U,B]=c.useState(!1);c.useEffect(()=>{N&&s&&!i&&(C.success("Profile updated successfully!"),a(M()),_(!1),a(S())),N&&t&&u&&(C.error(u.message||"Failed to update profile"),a(M()),_(!1))},[s,t,u,i,a,N]);const F=m=>{const{name:n,value:h}=m.target;p({...d,[n]:h})},L=async m=>{m.preventDefault(),_(!0);try{let n=d.profileImage;j&&(n=(await a(G(j)).unwrap()).data.fileUrl);const h={firstName:d.firstName,lastName:d.lastName,profileImage:n};a(K(h))}catch{C.error("Failed to upload image or update profile"),_(!1)}},Y=m=>{const n=m.target.files[0];if(n){l(n),B(!1);const h=new FileReader;h.onloadend=()=>{R(h.result)},h.readAsDataURL(n)}},$=()=>{B(!0)},T=()=>{window.confirm("Are you sure you want to delete your account? This action cannot be undone.")};return e.jsx("div",{className:"BuyerProfile",children:e.jsx(y,{icon:e.jsx(v,{className:"BuyerSidebar__icon"}),title:"My Profile",children:e.jsxs("div",{className:"profile_border_container",children:[e.jsxs("div",{className:"BuyerProfile__container",children:[e.jsxs("div",{className:"BuyerProfile__left-section",children:[e.jsxs("div",{className:"BuyerProfile__form-row",children:[e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(v,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:d.firstName,onChange:F,placeholder:"First Name",required:!0,className:"BuyerProfile__input"})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(v,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:d.lastName,onChange:F,placeholder:"Last Name",required:!0,className:"BuyerProfile__input"})]})})]}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(V,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:d.email,placeholder:"Email Address",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})}),e.jsx("div",{className:"BuyerProfile__input-field",children:e.jsxs("div",{className:"BuyerProfile__input-container",children:[e.jsx("div",{className:"BuyerProfile__input-icon",children:e.jsx(O,{})}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:d.mobile,placeholder:"Mobile Number",className:"BuyerProfile__input BuyerProfile__input--readonly",readOnly:!0,disabled:!0})]})})]}),e.jsx("div",{className:"BuyerProfile__right-section",children:e.jsxs("div",{className:"BuyerProfile__image-container",children:[e.jsx("h3",{className:"BuyerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"BuyerProfile__image",children:A||d.profileImage&&!U?e.jsx("img",{src:A||W(d.profileImage),alt:"Profile",onError:$}):e.jsx("div",{className:"BuyerProfile__placeholder",children:d.firstName&&d.lastName?`${d.firstName.charAt(0)}${d.lastName.charAt(0)}`:e.jsx(v,{className:"BuyerProfile__user-icon"})})}),e.jsx("button",{className:"BuyerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:Y,style:{display:"none"}})]})})]}),e.jsxs("div",{className:"BuyerProfile__buttons mt-30",children:[e.jsx("button",{type:"button",className:"BuyerProfile__delete-btn",onClick:T,children:"Delete Account"}),e.jsx("button",{type:"button",className:"BuyerProfile__save-btn",onClick:L,disabled:N||i,children:N||i?"Updating...":"Update & Save"})]})]})})})},xe=()=>{const a=o(Q),r=X(),i=[{key:"no",label:"No.",className:"no"},{key:"orderId",label:"Order Id",className:"order-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"amount",label:"Amount",className:"amount"},{key:"status",label:"Status",className:"status"},{key:"action",label:"Action",className:"action"}],s=(t,u)=>e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"table-cell no",children:u+1}),e.jsxs("div",{className:"table-cell order-id",children:["#",t.id,"245578"]}),e.jsx("div",{className:"table-cell video",children:e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:"https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:t.title})}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:t.title}),e.jsxs("div",{className:"content-coach",children:["By ",t.coach]})]})]})}),e.jsxs("div",{className:"table-cell date",children:[t.downloadDate," | 4:30PM"]}),e.jsx("div",{className:"table-cell amount",children:"$22.00"}),e.jsx("div",{className:"table-cell status",children:e.jsx("span",{className:"status-badge downloaded",children:"Downloaded"})}),e.jsx("div",{className:"table-cell action",children:e.jsx("button",{className:"action-btn",onClick:()=>r(`/buyer/download-details/${t.id}`),children:e.jsx(P,{})})})]});return e.jsx("div",{className:"BuyerDownloads",children:e.jsx(y,{icon:e.jsx(Z,{className:"BuyerSidebar__icon"}),title:"My Downloads",children:a.length>0?e.jsx(D,{columns:i,data:a,renderRow:s,variant:"grid",className:"BuyerDownloads__table",emptyMessage:"You have no downloads yet."}):e.jsx("div",{className:"BuyerDownloads__empty",children:e.jsx("p",{children:"You have no downloads yet."})})})})},Ne=()=>{const a=o(z),r=[{key:"no",label:"No.",className:"no"},{key:"requestId",label:"Request Id",className:"request-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"requestedAmount",label:"Requested Amount",className:"requested-amount"},{key:"status",label:"Status",className:"status"},{key:"action",label:"Action",className:"action"}],i=(s,t)=>e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"table-cell no",children:t+1}),e.jsxs("div",{className:"table-cell request-id",children:["#REQUEST",s.id]}),e.jsx("div",{className:"table-cell video",children:e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:"https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:s.title})}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:s.title}),e.jsx("div",{className:"content-coach",children:"By Coach"})]})]})}),e.jsxs("div",{className:"table-cell date",children:[s.date," | 4:30PM"]}),e.jsx("div",{className:"table-cell requested-amount",children:"$22.00"}),e.jsx("div",{className:"table-cell status",children:e.jsx("span",{className:`status-badge ${s.status}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})}),e.jsx("div",{className:"table-cell action",children:e.jsx("button",{className:"action-btn",children:e.jsx(P,{})})})]});return e.jsx("div",{className:"BuyerRequests",children:e.jsx(y,{icon:e.jsx(H,{className:"BuyerSidebar__icon"}),title:"My Requests",children:a.length>0?e.jsx(D,{columns:r,data:a,renderRow:i,variant:"grid",className:"BuyerRequests__table",emptyMessage:"You have no requests yet."}):e.jsx("div",{className:"BuyerRequests__empty",children:e.jsx("p",{children:"You have no requests yet."})})})})},ye=()=>{const a=o(J),r=[{key:"no",label:"No.",className:"no"},{key:"bidId",label:"Bid Id",className:"bid-id"},{key:"video",label:"Videos/Documents",className:"video"},{key:"date",label:"Date",className:"date"},{key:"bidAmount",label:"Bid Amount",className:"bid-amount"},{key:"status",label:"Status",className:"status"},{key:"action",label:"Action",className:"action"}],i=(s,t)=>e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"table-cell no",children:t+1}),e.jsx("div",{className:"table-cell bid-id",children:"#12245578"}),e.jsx("div",{className:"table-cell video",children:e.jsxs("div",{className:"content-item",children:[e.jsx("div",{className:"content-image",children:e.jsx("img",{src:"https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:s.title})}),e.jsxs("div",{className:"content-info",children:[e.jsx("div",{className:"content-title",children:s.title}),e.jsxs("div",{className:"content-coach",children:["By ",s.coach]})]})]})}),e.jsxs("div",{className:"table-cell date",children:[s.date," | 4:30PM"]}),e.jsxs("div",{className:"table-cell bid-amount",children:["$",s.bidAmount.toFixed(2)]}),e.jsx("div",{className:"table-cell status",children:e.jsx("span",{className:`status-badge ${s.status}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})}),e.jsx("div",{className:"table-cell action",children:e.jsx("button",{className:"action-btn",children:e.jsx(P,{})})})]});return e.jsx("div",{className:"BuyerBids",children:e.jsx(y,{icon:e.jsx(ee,{className:"BuyerSidebar__icon"}),title:"My Bids",children:a.length>0?e.jsx(D,{columns:r,data:a,renderRow:i,variant:"grid",className:"BuyerBids__table",emptyMessage:"You have no bids yet."}):e.jsx("div",{className:"BuyerBids__empty",children:e.jsx("p",{children:"You have no bids yet."})})})})},pe=()=>{const a=w(),r=o(ae),i=o(se),s=o(le),t=()=>{a(I(i==="list"?"add":"list")),i==="add"&&a(q())},u=l=>{window.confirm("Are you sure you want to remove this card?")&&a(ce(l))},d=l=>{a(f({nameOnCard:l}))},p=l=>{a(f({cardNumber:l}))},k=l=>{a(f({expiryDate:l}))},b=l=>{a(f({cvv:l}))},j=()=>{const l={id:Date.now().toString(),lastFourDigits:s.cardNumber.slice(-4),cardType:"mastercard"};a(ne(l)),a(q()),a(I("list"))};return e.jsx("div",{className:"BuyerCards",children:e.jsx(y,{icon:e.jsx(g,{className:"BuyerSidebar__icon"}),title:"My Cards",children:e.jsx("div",{className:"buyercardsbordercontainer",children:i==="list"?e.jsxs("div",{className:"BuyerCards__list-view",children:[e.jsxs("div",{className:"BuyerCards__header",children:[e.jsx("h3",{className:"BuyerCards__subtitle",children:"Saved Cards"}),e.jsxs("button",{className:"BuyerCards__add-btn",onClick:t,children:[e.jsx(te,{})," Add New Card"]})]}),e.jsx("div",{className:"BuyerCards__cards-list",children:r.length>0?r.map(l=>e.jsxs("div",{className:"BuyerCards__card-item",children:[e.jsxs("div",{className:"BuyerCards__card-content",children:[e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:"Mastercard"})}),e.jsxs("div",{className:"BuyerCards__card-number",children:["•••• •••• •••• ",l.lastFourDigits]})]}),e.jsx("button",{className:"BuyerCards__delete-btn",onClick:()=>u(l.id),"aria-label":"Delete card",children:e.jsx(re,{})})]},l.id)):e.jsx("div",{className:"BuyerCards__empty-state",children:e.jsx("p",{children:"You have no saved payment methods yet."})})})]}):e.jsxs("div",{className:"BuyerCards__add-view",children:[e.jsx("div",{className:"BuyerCards__header",children:e.jsx("h3",{className:"BuyerCards__subtitle",children:"Add New Card"})}),e.jsx("div",{className:"BuyerCards__form",children:e.jsxs("form",{onSubmit:l=>{l.preventDefault(),j()},children:[e.jsx("div",{className:"BuyerCards__form-row",children:e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--full",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(g,{})}),e.jsx("input",{type:"text",id:"nameOnCard",name:"nameOnCard",value:s.nameOnCard,onChange:l=>d(l.target.value),placeholder:"Name on card",required:!0,className:"BuyerCards__input"})]})})}),e.jsxs("div",{className:"BuyerCards__form-row",children:[e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--card-number",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(g,{})}),e.jsx("input",{type:"text",id:"cardNumber",name:"cardNumber",value:s.cardNumber,onChange:l=>p(l.target.value),placeholder:"Card Number",required:!0,maxLength:19,pattern:"[0-9\\\\s]{13,19}",className:"BuyerCards__input"})]})}),e.jsx("div",{className:"BuyerCards__card-logo",children:e.jsx("img",{src:"https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",alt:"Mastercard"})})]}),e.jsxs("div",{className:"BuyerCards__form-row",children:[e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--half",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(ie,{})}),e.jsx("input",{type:"text",id:"expiryDate",name:"expiryDate",value:s.expiryDate,onChange:l=>k(l.target.value),placeholder:"MM/YY",required:!0,maxLength:5,pattern:"^(0[1-9]|1[0-2])\\/([0-9]{2})$",className:"BuyerCards__input"})]})}),e.jsx("div",{className:"BuyerCards__input-field BuyerCards__input-field--half",children:e.jsxs("div",{className:"BuyerCards__input-container",children:[e.jsx("div",{className:"BuyerCards__input-icon",children:e.jsx(de,{})}),e.jsx("input",{type:"text",id:"cvv",name:"cvv",value:s.cvv,onChange:l=>b(l.target.value.replace(/\D/g,"")),placeholder:"CVV",required:!0,maxLength:4,pattern:"[0-9]{3,4}",className:"BuyerCards__input"})]})})]}),e.jsx("div",{className:"BuyerCards__form-actions",children:e.jsx("button",{type:"submit",className:"BuyerCards__submit-btn",children:"Add Card"})})]})}),e.jsx("div",{className:"BuyerCards__form-actions",children:e.jsx("button",{className:"BuyerCards__cancel-btn",onClick:t,children:"Cancel"})})]})})})})},ge=()=>{const a=w(),r=oe(),i=o(ue);c.useEffect(()=>{const t=r.pathname;t.includes("/dashboard")?a(x("dashboard")):t.includes("/profile")?a(x("profile")):t.includes("/downloads")?a(x("downloads")):t.includes("/requests")?a(x("requests")):t.includes("/bids")?a(x("bids")):t.includes("/cards")?a(x("cards")):a(x("dashboard"))},[r.pathname,a]);const s=()=>{switch(i){case"dashboard":return e.jsx(E,{});case"profile":return e.jsx(he,{});case"downloads":return e.jsx(xe,{});case"requests":return e.jsx(Ne,{});case"bids":return e.jsx(ye,{});case"cards":return e.jsx(pe,{});default:return e.jsx(E,{})}};return e.jsx("div",{className:"BuyerAccount",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(me,{})}),e.jsx("div",{className:"content",children:s()})]})})};export{ge as default};
