import{G as n,ao as r,e as c,aw as o,j as e,ax as u}from"./index-CbuIrTWR.js";import{S as d}from"./SellerLayout-CjHDmTwH.js";import{T as m}from"./Table-CPAiTSiF.js";import{S as _}from"./index-B_Bht8JV.js";function h(t){return n({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"},child:[]},{tag:"path",attr:{d:"M13 8H7"},child:[]},{tag:"path",attr:{d:"M17 12H7"},child:[]}]})(t)}const q=(t,a)=>{const s=a.find(i=>i.id===`#${t}`);return s?{...s,time:"4:50PM",customer:s.customer||{name:s.requestedCustomer,email:`${s.requestedCustomer.toLowerCase().replace(" ",".")}@email.com`,phone:"************"},history:[{no:1,requestId:`#${t}`,requestedCustomer:s.requestedCustomer,date:s.date,price:s.price,requestedAmount:s.requestedAmount,status:"Pending",offerAmount:"-"},{no:2,requestId:`#${t}`,requestedCustomer:s.requestedCustomer,date:s.date,price:s.price,requestedAmount:s.requestedAmount,status:"Accepted",offerAmount:"-"},{no:3,requestId:`#${t}`,requestedCustomer:s.requestedCustomer,date:s.date,price:s.price,requestedAmount:s.requestedAmount,status:"Counter Offer",offerAmount:"$17.00"}]}:{id:`#${t}`,title:"Frank Martin - Drills and Coaching Philosophies to Developing Toughness in Your Players",subtitle:"Basketball Coaching Clinic",date:"20 May 2025",time:"4:50PM",price:"$22.00",requestedAmount:"$19.00",image:"https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop",customer:{name:"John Smith",email:"<EMAIL>",phone:"************"},history:[{no:1,requestId:`#${t}`,requestedCustomer:"John Smith",date:"20 May 2025",price:"$22.00",requestedAmount:"$19.00",status:"Pending",offerAmount:"-"}]}},N=()=>{const{id:t}=r(),a=c(o),s=q(t,a),i=[{key:"no",label:"No."},{key:"requestId",label:"Request Id"},{key:"requestedCustomer",label:"Requested Customer"},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"requestedAmount",label:"Requested Amount"},{key:"status",label:"Status",render:l=>e.jsx("span",{className:`status-badge status-${l.status.toLowerCase().replace(" ","-")}`,children:l.status})},{key:"offerAmount",label:"Offer Amount"},{key:"action",label:"Action",render:()=>e.jsxs("div",{className:"action-icons",children:[e.jsx("button",{className:"action-btn view-btn",children:e.jsx(_,{})}),e.jsx("button",{className:"action-btn edit-btn",children:e.jsx(u,{})})]})}];return e.jsx(d,{children:e.jsx("div",{className:"RequestDetails",children:e.jsxs("div",{className:"RequestDetails__content",children:[e.jsxs("div",{className:"RequestDetails__main-section",children:[e.jsx("div",{className:"RequestDetails__header",children:e.jsxs("div",{className:"RequestDetails__content-info",children:[e.jsx("img",{src:s.image,alt:s.title,className:"RequestDetails__content-image"}),e.jsxs("div",{className:"RequestDetails__content-details",children:[e.jsx("h3",{className:"RequestDetails__content-title",children:s.title}),e.jsx("p",{className:"RequestDetails__content-subtitle",children:s.subtitle})]})]})}),e.jsxs("div",{className:"RequestDetails__info-grid",children:[e.jsx("h3",{className:"RequestDetails__section-title",children:"Request Information"}),e.jsxs("div",{className:"RequestDetails__info-section",children:[e.jsxs("div",{className:"RequestDetails__info-item-grid",children:[e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Request Id:"}),e.jsx("span",{className:"RequestDetails__info-value",children:s.id})]}),e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Date:"}),e.jsxs("span",{className:"RequestDetails__info-value",children:[s.date," | ",s.time]})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsxs("div",{className:"RequestDetails__info-item-grid",children:[e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Price:"}),e.jsx("span",{className:"RequestDetails__info-value",children:s.price})]}),e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Requested Amount:"}),e.jsx("span",{className:"RequestDetails__info-value",children:s.requestedAmount})]})]})]}),e.jsx("h3",{className:"RequestDetails__section-title",children:"Customer Details"}),e.jsxs("div",{className:"RequestDetails__customer-section",children:[e.jsxs("div",{className:"RequestDetails__info-item-grid",children:[e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Name:"}),e.jsx("span",{className:"RequestDetails__info-value",children:s.customer.name})]}),e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Email Address:"}),e.jsx("span",{className:"RequestDetails__info-value",children:s.customer.email})]}),e.jsxs("div",{className:"RequestDetails__info-item",children:[e.jsx("span",{className:"RequestDetails__info-label",children:"Phone Number:"}),e.jsx("span",{className:"RequestDetails__info-value",children:s.customer.phone})]})]}),e.jsx("div",{className:"vertical-line"}),e.jsx("div",{className:"RequestDetails__info-item-grid requestDetails-btn-grid",children:e.jsxs("button",{className:" btn-outline",children:[e.jsx(h,{})," Message"]})})]})]})]}),e.jsxs("div",{className:"RequestDetails__actions",children:[e.jsx("button",{className:"RequestDetails__btn RequestDetails__btn--accept",children:"Accepted"}),e.jsx("button",{className:"RequestDetails__btn btn-primary",children:"Rejected"}),e.jsx("button",{className:" btn-outline",children:"Counter Offer"})]}),e.jsxs("div",{className:"RequestDetails__history-section",children:[e.jsx("h3",{className:"RequestDetails__section-title",children:"History"}),e.jsx(m,{columns:i,data:s.history,className:"RequestDetails__history-table"})]})]})})})};export{N as default};
