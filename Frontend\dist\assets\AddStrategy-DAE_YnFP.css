.AddStrategy{width:100%;max-width:100%}.AddStrategy__form{display:flex;flex-direction:column;gap:var(--heading5)}.AddStrategy__field{display:flex;flex-direction:column;gap:var(--smallfont)}.AddStrategy__label{font-size:var(--basefont);font-weight:600;color:var(--secondary-color);margin-bottom:var(--smallfont)}.AddStrategy__input,.AddStrategy__select,.AddStrategy__textarea{padding:var(--smallfont) var(--basefont);border:1px solid var(--light-gray);border-radius:var(--border-radius);font-size:var(--basefont);color:var(--text-color);background-color:var(--white);transition:border-color .3s ease,box-shadow .3s ease;outline:none;font-family:inherit}.AddStrategy__input:focus,.AddStrategy__select:focus,.AddStrategy__textarea:focus{border-color:var(--btn-color);box-shadow:0 0 0 2px #ee34251a}.AddStrategy__input::placeholder,.AddStrategy__textarea::placeholder{color:var(--dark-gray);opacity:.7}.AddStrategy__textarea{min-height:120px;resize:vertical;line-height:1.5}.AddStrategy__select{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right var(--smallfont) center;background-size:16px;padding-right:var(--heading4)}.AddStrategy__upload{border:2px dashed var(--light-gray);border-radius:var(--border-radius);padding:var(--heading4);text-align:center;background-color:var(--bg-gray);transition:all .3s ease;cursor:pointer}.AddStrategy__upload:hover{border-color:var(--btn-color);background-color:#ee34250d}.AddStrategy__upload-content{display:flex;flex-direction:column;align-items:center;gap:var(--smallfont)}.AddStrategy__upload-icon{font-size:var(--heading3);color:var(--dark-gray);margin-bottom:var(--smallfont)}.AddStrategy__upload-text{font-size:var(--basefont);font-weight:500;color:var(--text-color);margin:0}.AddStrategy__upload-subtext{font-size:var(--smallfont);color:var(--dark-gray);margin:0}.AddStrategy__actions{display:flex;gap:var(--basefont);justify-content:flex-start;margin-top:var(--heading5);padding-top:var(--heading5);border-top:1px solid var(--light-gray)}.AddStrategy__submit-btn,.AddStrategy__reset-btn{padding:var(--smallfont) var(--heading5);font-size:var(--basefont);font-weight:600;border-radius:var(--border-radius);cursor:pointer;transition:all .3s ease}@media (max-width: 768px){.AddStrategy__form{gap:var(--basefont)}.AddStrategy__actions{flex-direction:column;gap:var(--smallfont)}.AddStrategy__submit-btn,.AddStrategy__reset-btn{width:100%;justify-content:center}.AddStrategy__upload{padding:var(--basefont)}.AddStrategy__upload-icon{font-size:var(--heading4)}}@media (max-width: 480px){.AddStrategy__label{font-size:var(--smallfont)}.AddStrategy__input,.AddStrategy__select,.AddStrategy__textarea{padding:var(--extrasmallfont) var(--smallfont);font-size:var(--smallfont)}.AddStrategy__upload{padding:var(--smallfont)}.AddStrategy__upload-text{font-size:var(--smallfont)}.AddStrategy__upload-subtext{font-size:var(--extrasmallfont)}}
