import{r as c,j as e,F as f,b as N,al as j,am as g,an as y}from"./index-CbuIrTWR.js";import{S as I}from"./StrategyCard-DatvVn-L.js";import{t as v}from"./thankyou-zEUM9FAJ.js";const D=({isOpen:t,onClose:d,strategy:m})=>{const[a,u]=c.useState(""),[r,i]=c.useState(!1),[h,x]=c.useState({days:0,hours:1,minutes:59,seconds:30}),_=[{no:1,bidId:"#2024/05",date:"20 May 2024 | 4:50PM",bidAmount:"$20.00"},{no:2,bidId:"#2024/04",date:"20 May 2024 | 4:45PM",bidAmount:"$22.00"},{no:3,bidId:"#2024/03",date:"20 May 2024 | 4:30PM",bidAmount:"$22.00"},{no:4,bidId:"#2024/02",date:"20 May 2024 | 4:30PM",bidAmount:"$22.00"},{no:5,bidId:"#2024/01",date:"20 May 2024 | 4:30PM",bidAmount:"$22.00"}],l=c.useCallback(()=>{i(!1),u(""),d()},[d]);c.useEffect(()=>{if(!t)return;const s=setInterval(()=>{x(n=>n.seconds>0?{...n,seconds:n.seconds-1}:n.minutes>0?{...n,minutes:n.minutes-1,seconds:59}:n.hours>0?{...n,hours:n.hours-1,minutes:59,seconds:59}:n.days>0?{...n,days:n.days-1,hours:23,minutes:59,seconds:59}:n)},1e3);return()=>clearInterval(s)},[t]),c.useEffect(()=>{const s=n=>{n.key==="Escape"&&l()};return t&&(document.addEventListener("keydown",s),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",s),document.body.style.overflow="unset"}},[t,l]),c.useEffect(()=>{if(r){const s=setTimeout(()=>{l()},3e3);return()=>clearTimeout(s)}},[r,l]);const b=s=>{s.preventDefault(),a.trim()&&(console.log("Bid submitted:",a),i(!0))},p=s=>{s.target===s.currentTarget&&l()};return t?e.jsx("div",{className:"bid-modal-overlay",onClick:p,children:e.jsxs("div",{className:`bid-modal ${r?"bid-modal--success":""}`,children:[e.jsxs("div",{className:"bid-modal__header",children:[e.jsx("h2",{className:"bid-modal__title",children:r?"Your bid is submitted successfully!":"Bid is Available Now!"}),e.jsx("button",{className:"bid-modal__close",onClick:l,children:e.jsx(f,{})})]}),e.jsx("div",{className:"bid-modal__content",children:r?e.jsxs("div",{className:"bid-modal__success",children:[e.jsx("div",{className:"success-icon",children:e.jsx("img",{src:v,alt:"Success"})}),e.jsx("p",{className:"success-message",children:"Thank you for your bid! We will notify you about the bidding results."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bid-modal__countdown",children:[e.jsxs("div",{className:"countdown-item",children:[e.jsx("span",{className:"countdown-number",children:String(h.days).padStart(2,"0")}),e.jsx("span",{className:"countdown-label",children:"DAYS"})]}),e.jsxs("div",{className:"countdown-item",children:[e.jsx("span",{className:"countdown-number",children:String(h.hours).padStart(2,"0")}),e.jsx("span",{className:"countdown-label",children:"HOURS"})]}),e.jsxs("div",{className:"countdown-item",children:[e.jsx("span",{className:"countdown-number",children:String(h.minutes).padStart(2,"0")}),e.jsx("span",{className:"countdown-label",children:"MINUTES"})]}),e.jsxs("div",{className:"countdown-item",children:[e.jsx("span",{className:"countdown-number",children:String(h.seconds).padStart(2,"0")}),e.jsx("span",{className:"countdown-label",children:"SECONDS"})]})]}),e.jsxs("div",{className:"bid-modal__form-section",children:[e.jsx("h3",{className:"bid-modal__form-title",children:"Enter Your Bid Amount"}),e.jsxs("form",{onSubmit:b,className:"bid-modal__form",children:[e.jsx("input",{type:"number",step:"0.01",min:"0",value:a,onChange:s=>u(s.target.value),placeholder:"Enter Bid Amount",className:"bid-modal__input",required:!0}),e.jsx("button",{type:"submit",className:"bid-modal__submit-btn",children:"SUBMIT BID"})]})]}),e.jsxs("div",{className:"bid-modal__history-section",children:[e.jsx("h3",{className:"bid-modal__history-title",children:"Others Bid"}),e.jsx("div",{className:"bid-modal__table-container",children:e.jsxs("table",{className:"bid-modal__table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"No."}),e.jsx("th",{children:"Bid Id"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Bid Amount"})]})}),e.jsx("tbody",{children:_.map(s=>e.jsxs("tr",{children:[e.jsx("td",{children:s.no}),e.jsx("td",{children:s.bidId}),e.jsx("td",{children:s.date}),e.jsx("td",{children:s.bidAmount})]},s.no))})]})})]})]})})]})}):null},C=({isOpen:t,onClose:d})=>{const[m,a]=c.useState({name:"",email:"",phone:"",message:""}),[u,r]=c.useState(!1),i=l=>{const{name:b,value:p}=l.target;a(s=>({...s,[b]:p}))},h=l=>{l.preventDefault(),console.log("Custom training request submitted:",m),r(!0)},x=()=>{a({name:"",email:"",phone:"",message:""}),r(!1),d()},_=l=>{l.target===l.currentTarget&&x()};return t?e.jsx("div",{className:"request-modal-overlay",onClick:_,children:e.jsxs("div",{className:`request-modal ${u?"request-modal--success":""}`,children:[e.jsxs("div",{className:"request-modal__header",children:[e.jsx("h2",{className:"request-modal__title",children:u?"Your request is submitted successfully!":"Request Custom Training"}),e.jsx("button",{className:"request-modal__close",onClick:x,children:e.jsx(f,{})})]}),e.jsx("div",{className:"request-modal__content",children:u?e.jsxs("div",{className:"request-modal__success",children:[e.jsx("div",{className:"success-icon",children:e.jsx(N,{})}),e.jsx("p",{className:"success-message",children:"Your custom training request has been submitted successfully! We will get back to you soon."})]}):e.jsxs("form",{onSubmit:h,className:"request-modal__form",children:[e.jsxs("div",{className:"request-modal__form-group",children:[e.jsx("label",{htmlFor:"name",className:"request-modal__label",children:"Full Name"}),e.jsx("input",{type:"text",id:"name",name:"name",value:m.name,onChange:i,className:"request-modal__input",placeholder:"Enter your full name",required:!0})]}),e.jsxs("div",{className:"request-modal__form-group",children:[e.jsx("label",{htmlFor:"email",className:"request-modal__label",children:"Email Address"}),e.jsx("input",{type:"email",id:"email",name:"email",value:m.email,onChange:i,className:"request-modal__input",placeholder:"Enter your email address",required:!0})]}),e.jsxs("div",{className:"request-modal__form-group",children:[e.jsx("label",{htmlFor:"phone",className:"request-modal__label",children:"Phone Number"}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:m.phone,onChange:i,className:"request-modal__input",placeholder:"Enter your phone number",required:!0})]}),e.jsxs("div",{className:"request-modal__form-group",children:[e.jsx("label",{htmlFor:"message",className:"request-modal__label",children:"Message"}),e.jsx("textarea",{id:"message",name:"message",value:m.message,onChange:i,className:"request-modal__textarea",placeholder:"Tell us about your custom training requirements...",rows:"4",required:!0})]}),e.jsx("button",{type:"submit",className:"request-modal__submit-btn",children:"Send Request"})]})})]})}):null},o={id:1,title:"Frank Martin - Drills And Coaching Philosophies To Developing Toughness In Your Players",coach:"Basketball Coaching Clinic",price:22,image:"https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",category:"Basketball/Player Development",bookings:247,duration:"1h 25",type:"All New Content",description:"In This Course, Coach Frank Martin Presents 'Drills And Coaching Philosophies To Developing Toughness In Your Players To Win On The Court And In Life'. Coach Martin Begins With How Coaching Has Changed Over The Years And Some Of The High School Coaches. Coach Then Goes Into Teams Are Famous For Coach Martin Also Goes About For College Coaches, Continuing With His Favorite Drills Which Helps Teach A Certain Level Of Toughness.",includes:["24 Hours On-Demand Video","Streaming From Mobile And TV","Lifetime Access","Certificate Of Completion","100% Money Back Guarantee"]},q=[{id:1,question:"Is simply dummy text of the printing and typesetting industry?",answer:"Order a relaxon online and we will reach out to you to schedule delivery."},{id:2,question:"What is Lorem Ipsum?",answer:"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s."},{id:3,question:"What is dummy text?",answer:"Dummy text is placeholder text commonly used to demonstrate the visual form of a document or a typeface without relying on meaningful content."},{id:4,question:"What is dummy text of the printing?",answer:"It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout."},{id:5,question:"What is simply dummy text?",answer:"There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form."}],w=[{id:2,title:"John Calipari - Early Transition Offensive Concepts",coach:"Basketball Coaching",price:24.99,image:"https://images.unsplash.com/photo-1574623452334-1e0ac2b3ccb4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",hasVideo:!0,type:"buy"},{id:3,title:"WR Fundamentals RPOs - Herman Wiggins",coach:"Football Coaching",price:19.99,image:"https://images.unsplash.com/photo-1560272564-c83b66b1ad12?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",hasVideo:!0,type:"buy"},{id:4,title:"Mastering the Ball with Anson Dorrance",coach:"Soccer Coaching",price:24.99,image:"https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",hasVideo:!0,type:"buy"},{id:5,title:"Triplanar Training: A systematic approach to elite speed and explosive...",coach:"Fitness Training",price:19.99,image:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",hasVideo:!0,type:"buy"}],S=()=>{const[t,d]=c.useState(null),m=a=>{d(t===a?null:a)};return e.jsx("div",{className:"faq-accordion",children:q.map(a=>e.jsxs("div",{className:"faq-item",children:[e.jsxs("button",{className:`faq-question ${t===a.id?"faq-question--active":""}`,onClick:()=>m(a.id),children:[e.jsx("span",{className:"faq-question-text",children:a.question}),e.jsx("span",{className:"faq-icon",children:t===a.id?e.jsx(g,{}):e.jsx(y,{})})]}),e.jsx("div",{className:`faq-answer ${t===a.id?"faq-answer--open":""}`,children:e.jsx("div",{className:"faq-answer-content",children:a.answer})})]},a.id))})},B=()=>{const[t,d]=c.useState("description"),[m,a]=c.useState(!1),[u,r]=c.useState(!1);return e.jsxs("div",{className:"ItemDetail",children:[e.jsx("div",{className:"ItemDetail__container max-container",children:e.jsxs("div",{className:"ItemDetail__content",children:[e.jsxs("div",{className:"ItemDetail__mainContent",children:[e.jsx("h1",{className:"ItemDetail__title",children:o.title}),e.jsxs("p",{className:"ItemDetail__coach",children:["By ",o.coach]}),e.jsx("div",{className:"ItemDetail__imageContainer",children:e.jsx("img",{src:o.image,alt:o.title,className:"ItemDetail__image"})}),e.jsxs("div",{className:"ItemDetail__tabs",children:[e.jsxs("div",{className:"ItemDetail__tabButtons",children:[e.jsx("button",{className:`ItemDetail__tabButton ${t==="description"?"ItemDetail__tabButton--active":""}`,onClick:()=>d("description"),children:"Description"}),e.jsx("button",{className:`ItemDetail__tabButton ${t==="coach"?"ItemDetail__tabButton--active":""}`,onClick:()=>d("coach"),children:"The Coach"}),e.jsx("button",{className:`ItemDetail__tabButton ${t==="faqs"?"ItemDetail__tabButton--active":""}`,onClick:()=>d("faqs"),children:"FAQs"})]}),e.jsxs("div",{className:"ItemDetail__tabContent",children:[t==="description"&&e.jsxs("div",{className:"ItemDetail__tabPanel",children:[e.jsx("p",{className:"ItemDetail__description",children:o.description}),e.jsxs("div",{className:"seemoreContainer",children:[e.jsx("a",{href:"#",className:"ItemDetail__seeMore",children:"See More"}),e.jsx(j,{})]})]}),t==="coach"&&e.jsxs("div",{className:"ItemDetail__tabPanel",children:[e.jsx("p",{className:"ItemDetail__description",children:"Coach Frank Martin is a renowned basketball coach with over 20 years of experience in developing player toughness and mental resilience. His coaching philosophy focuses on building character both on and off the court."}),e.jsxs("div",{className:"seemoreContainer",children:[e.jsx("a",{href:"#",className:"ItemDetail__seeMore",children:"See More"}),e.jsx(j,{})]})]}),t==="faqs"&&e.jsx("div",{className:"ItemDetail__tabPanel",children:e.jsx(S,{})})]})]})]}),e.jsxs("aside",{className:"ItemDetail__sidebar",children:[e.jsxs("div",{className:"ItemDetail__priceBox",children:[e.jsxs("div",{className:"ItemDetail__price",children:[e.jsx("p",{children:"Price"}),"$",o.price.toFixed(2)]}),e.jsx("button",{className:"ItemDetail__buyButton btn-primary",onClick:()=>a(!0),children:"Place Bid/Offer"}),e.jsx("button",{className:"ItemDetail__Request_Custom_Training",onClick:()=>r(!0),children:"Request Custom Training"})]}),e.jsxs("div",{className:"ItemDetail__contentIncludes",children:[e.jsx("h3",{className:"ItemDetail__sidebarTitle",children:"This Strategic Content Includes"}),e.jsx("ul",{className:"ItemDetail__includesList",children:o.includes.map((i,h)=>e.jsx("li",{className:"ItemDetail__includesItem",children:i},h))})]}),e.jsxs("div",{className:"ItemDetail__contentInfo",children:[e.jsx("h3",{className:"ItemDetail__sidebarTitle",children:"Strategic Content Info"}),e.jsxs("div",{className:"ItemDetail__infoList",children:[e.jsxs("div",{className:"ItemDetail__infoItem",children:[e.jsx("span",{className:"ItemDetail__infoLabel",children:"Category:"}),e.jsx("span",{className:"ItemDetail__infoValue",children:o.category})]}),e.jsxs("div",{className:"ItemDetail__infoItem",children:[e.jsx("span",{className:"ItemDetail__infoLabel",children:"Bookings:"}),e.jsx("span",{className:"ItemDetail__infoValue",children:o.bookings})]}),e.jsxs("div",{className:"ItemDetail__infoItem",children:[e.jsx("span",{className:"ItemDetail__infoLabel",children:"Duration:"}),e.jsx("span",{className:"ItemDetail__infoValue",children:o.duration})]}),e.jsxs("div",{className:"ItemDetail__infoItem",children:[e.jsx("span",{className:"ItemDetail__infoLabel",children:"Type:"}),e.jsx("span",{className:"ItemDetail__infoValue",children:o.type})]})]})]})]})]})}),e.jsx("section",{className:"ItemDetail__relatedSection",children:e.jsxs("div",{className:"max-container",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"ItemDetail__relatedTitle",children:"Related Sports Strategies Students Are Learning"}),e.jsx("a",{href:"#",className:"ItemDetail__learnMoreLink",children:"Learn More Contents"})]}),e.jsx("div",{className:"ItemDetail__relatedGrid",children:w.map(i=>e.jsx(I,{id:i.id,image:i.image,title:i.title,coach:i.coach,price:i.price,hasVideo:i.hasVideo,type:i.type},i.id))})]})}),e.jsx(D,{isOpen:m,onClose:()=>a(!1),strategy:o}),e.jsx(C,{isOpen:u,onClose:()=>r(!1),strategy:o})]})};export{B as default};
