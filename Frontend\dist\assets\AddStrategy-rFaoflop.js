import{G as a,j as e}from"./index-CbuIrTWR.js";import{S as l}from"./SellerLayout-CjHDmTwH.js";function s(t){return a({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"},child:[]},{tag:"polyline",attr:{points:"17 8 12 3 7 8"},child:[]},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"},child:[]}]})(t)}const n=()=>e.jsx(l,{children:e.jsx("div",{className:"AddStrategy",children:e.jsxs("div",{className:"AddStrategy__form",children:[e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Strategy Title"}),e.jsx("input",{type:"text",className:"AddStrategy__input",placeholder:"Add title for New Strategy",defaultValue:"Advanced Basketball Shooting Techniques"})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Video Category"}),e.jsxs("select",{className:"AddStrategy__select",defaultValue:"basketball",children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"basketball",children:"Basketball"}),e.jsx("option",{value:"football",children:"Football"}),e.jsx("option",{value:"soccer",children:"Soccer"}),e.jsx("option",{value:"baseball",children:"Baseball"}),e.jsx("option",{value:"tennis",children:"Tennis"}),e.jsx("option",{value:"golf",children:"Golf"}),e.jsx("option",{value:"other",children:"Other"})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Description for Strategy"}),e.jsx("textarea",{className:"AddStrategy__textarea",placeholder:"Enter strategy description...",defaultValue:"Enter a detailed description of your strategy here. Explain the key concepts, methodologies, and expected outcomes that buyers can expect from this strategy.",rows:6})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"About The Coach"}),e.jsx("textarea",{className:"AddStrategy__textarea",placeholder:"Tell us about yourself...",defaultValue:"Share your background, experience, and expertise. Tell potential buyers about your coaching philosophy, achievements, and what makes your approach unique.",rows:6})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Includes Strategic Content"}),e.jsx("textarea",{className:"AddStrategy__textarea",placeholder:"Describe what's included in this strategy...",defaultValue:"Outline what strategic content is included in this package. Detail the specific materials, resources, insights, and actionable steps provided to buyers.",rows:6})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Upload Strategy Video"}),e.jsx("div",{className:"AddStrategy__upload",children:e.jsxs("div",{className:"AddStrategy__upload-content",children:[e.jsx(s,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:"Drag & drop your video file here or click to browse"}),e.jsx("p",{className:"AddStrategy__upload-subtext",children:"Supported formats: MP4, MOV, AVI (Max size: 500MB)"})]})})]}),e.jsxs("div",{className:"AddStrategy__actions",children:[e.jsx("button",{className:"btn btn-primary AddStrategy__submit-btn",children:"Add New Strategy"}),e.jsx("button",{className:"btn btn-outline AddStrategy__reset-btn",children:"Reset Form"})]})]})})});export{n as default};
