import{r as x,j as e,a9 as q,d as D,u as B,e as _,aO as z,U as G,aP as W,aK as X,z as V,A as H,Y as K,aQ as Q}from"./index-CbuIrTWR.js";import{s as Z,a as $}from"./toast-5PAXudRI.js";const J=({formData:d,onInputChange:C,onExperienceChange:v,onAddExperience:k,onRemoveExperience:w,onNext:h,fieldErrors:i})=>{const[y,S]=x.useState(d.profilePic||null),[a,N]=x.useState(""),g=["jpg","jpeg","png"],u=10*1024*1024;x.useEffect(()=>{d.profilePic&&d.profilePic!==y&&S(d.profilePic)},[d.profilePic,y]);const T=l=>{if(l.size>u)return"File size must be less than 10MB";const t=l.name.split(".").pop().toLowerCase();return g.includes(t)?["image/jpeg","image/jpg","image/png","image/gif"].includes(l.type)?null:"Invalid file type. Please select a valid image file.":`Unsupported file format. Please use: ${g.join(", ").toUpperCase()}`},F=l=>{const t=l.target.files[0];if(t){N("");const f=T(t);if(f){N(f),l.target.value="";return}const b=new FileReader;b.onload=P=>{S(P.target.result)},b.readAsDataURL(t),C("selectedImageFile",t),console.log("Image selected for later upload:",t.name)}};return e.jsxs("div",{className:"seller-onboarding-step1-container max-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step active",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step",children:"2"})]}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"description-section",children:[e.jsx("div",{className:"section-title",children:"Description"}),e.jsxs("div",{className:"description-box",children:[e.jsx("textarea",{className:`description-textarea ${i!=null&&i.description?"error":""}`,placeholder:"Write Description..",rows:3,value:d.description,onChange:l=>C("description",l.target.value)}),(i==null?void 0:i.description)&&e.jsx("div",{className:"field-error",children:i.description})]})]}),e.jsxs("div",{className:"profile-experience-grid",children:[e.jsxs("div",{className:"profile-pic-section",children:[e.jsx("div",{className:"section-title",children:"Profile Pic"}),e.jsxs("div",{className:"avatar-upload",children:[e.jsx("div",{className:"avatar-placeholder",children:y||d.profilePic?e.jsx("img",{src:y||d.profilePic,alt:"Profile",className:"avatar-image"}):e.jsxs("svg",{width:"64",height:"64",viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"32",cy:"32",r:"32",fill:"var(--light-gray)"}),e.jsx("ellipse",{cx:"32",cy:"27",rx:"12",ry:"12",fill:"#fff"}),e.jsx("ellipse",{cx:"32",cy:"50",rx:"16",ry:"10",fill:"#fff"})]})}),e.jsx("input",{type:"file",id:"profilePicInput",accept:"image/jpeg,image/jpg,image/png,image/gif",onChange:F,style:{display:"none"}}),e.jsx("div",{className:"upload-buttons",children:e.jsx("button",{type:"button",className:"btn btn-outline upload-btn",onClick:()=>document.getElementById("profilePicInput").click(),children:"Choose Photo"})}),e.jsx("div",{className:"upload-info",children:e.jsxs("small",{className:"upload-format-info",children:["Supported formats: ",g.join(", ").toUpperCase()," (Max: 10MB)"]})}),a&&e.jsx("div",{className:"field-error image-error",children:a})]})]}),e.jsxs("div",{className:"experience-section",children:[e.jsx("div",{className:"section-title",children:"Experience"}),e.jsxs("div",{className:"experience-container",children:[d.experiences.map((l,t)=>{var f,b,P,A,I,M,L,O;return e.jsxs("div",{className:"experience-row",children:[e.jsxs("div",{className:"experience-row-content",children:[e.jsx("input",{type:"text",className:`input ${i!=null&&i.experiences&&t===0?"error":""}`,placeholder:"Enter School Name",value:l.schoolName,onChange:j=>v(t,"schoolName",j.target.value)}),e.jsx("input",{type:"text",className:"input",placeholder:"Enter Position",value:l.position,onChange:j=>v(t,"position",j.target.value)}),e.jsxs("div",{className:"year-fields",children:[e.jsxs("div",{children:[e.jsx("input",{type:"text",className:`input year-input ${(b=(f=i==null?void 0:i.experienceYears)==null?void 0:f[t])!=null&&b.fromYear?"error":""}`,placeholder:"From Year",value:l.fromYear,onChange:j=>v(t,"fromYear",j.target.value)}),((A=(P=i==null?void 0:i.experienceYears)==null?void 0:P[t])==null?void 0:A.fromYear)&&e.jsx("div",{className:"field-error",children:i.experienceYears[t].fromYear})]}),e.jsxs("div",{children:[e.jsx("input",{type:"text",className:`input year-input ${(M=(I=i==null?void 0:i.experienceYears)==null?void 0:I[t])!=null&&M.toYear?"error":""}`,placeholder:"To Year",value:l.toYear,onChange:j=>v(t,"toYear",j.target.value)}),((O=(L=i==null?void 0:i.experienceYears)==null?void 0:L[t])==null?void 0:O.toYear)&&e.jsx("div",{className:"field-error",children:i.experienceYears[t].toYear})]})]})]}),d.experiences.length>1&&w&&e.jsx("button",{type:"button",className:"delete-experience-btn",onClick:()=>w(t),title:"Remove this experience",children:e.jsx(q,{})})]},t)}),(i==null?void 0:i.experiences)&&e.jsx("div",{className:"field-error",children:i.experiences}),e.jsx("div",{className:"add-more-link",onClick:k,children:"+ Add More"})]})]})]})]}),e.jsx("div",{className:"next-btn-row",children:e.jsx("button",{className:"btn btn-primary next-btn",onClick:h,children:"Next"})})]})},ie=()=>{const d=D(),C=B(),{isLoading:v,isSuccess:k,isError:w,error:h,onboardingData:i}=_(s=>s.user),[y,S]=x.useState(1),[a,N]=x.useState({description:"",profilePic:"",selectedImageFile:null,experiences:[{schoolName:"",position:"",fromYear:"",toYear:""}],minTrainingCost:"",socialLinks:{facebook:"",instagram:"",twitter:""},sports:[],expertise:[],certifications:[]}),[g,u]=x.useState({description:"",experiences:"",minTrainingCost:"",experienceYears:{},profileImage:""}),[T,F]=x.useState(""),[l,t]=x.useState({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""});x.useEffect(()=>{document.body.style.pointerEvents="none";const s=document.querySelector(".seller-onboarding-wrapper");return s&&(s.style.pointerEvents="all"),()=>{document.body.style.pointerEvents="all"}},[]),x.useEffect(()=>{if(k&&(t({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""}),F(""),Z("Onboarding completed successfully! Welcome to XO Sports Hub!",{autoClose:4e3}),i&&d(z(i)),d(G()).then(()=>{d(W()),setTimeout(()=>{C("/seller/dashboard")},1500)})),w&&h){t({isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""});let s="An error occurred during onboarding. Please try again.";h.message?s=h.message:h.errors&&Array.isArray(h.errors)&&(s=h.errors.map(o=>o.msg).join(", ")),F(s),$(s)}},[k,w,h,d,C,i]);const f=(s,n)=>{console.log(`Updating ${s}:`,n),N(o=>({...o,[s]:n})),g[s]&&u(o=>({...o,[s]:""}))},b=()=>{const s=new Date().getFullYear(),n=1950,o=s,p={};let r=!1;return a.experiences.forEach((c,m)=>{const U=parseInt(c.fromYear),R=parseInt(c.toYear),Y={};String(c.fromYear||"").trim()?(isNaN(U)||U<n||U>o)&&(Y.fromYear=`From year must be between ${n} and ${o}`,r=!0):(Y.fromYear="From year is required",r=!0),String(c.toYear||"").trim()?isNaN(R)||R<n||R>o?(Y.toYear=`To year must be between ${n} and ${o}`,r=!0):!isNaN(U)&&R<U&&(Y.toYear="To year must be greater than or equal to from year",r=!0):(Y.toYear="To year is required",r=!0),Object.keys(Y).length>0&&(p[m]=Y)}),r?(u(c=>({...c,experienceYears:p})),!1):(u(c=>({...c,experienceYears:{}})),!0)},P=()=>{const s={};let n=!1;return a.description.trim()||(s.description="Description is required",n=!0),(a.experiences.length===0||!a.experiences[0].schoolName)&&(s.experiences="At least one experience with school name is required",n=!0),b()||(n=!0),n?(u(p=>({...p,...s})),!1):(u({description:"",experiences:"",minTrainingCost:"",experienceYears:{},profileImage:""}),!0)},A=()=>{P()&&S(2)};x.useEffect(()=>{console.log("Current formData.profilePic:",a.profilePic)},[a.profilePic]);const I=(s,n)=>{N(o=>({...o,socialLinks:{...o.socialLinks,[s]:n}}))},M=(s,n,o)=>{var r,c;const p=[...a.experiences];p[s]={...p[s],[n]:o},N(m=>({...m,experiences:p})),(n==="fromYear"||n==="toYear")&&(c=(r=g.experienceYears)==null?void 0:r[s])!=null&&c[n]&&u(m=>({...m,experienceYears:{...m.experienceYears,[s]:{...m.experienceYears[s],[n]:""}}}))},L=()=>{N(s=>({...s,experiences:[...s.experiences,{schoolName:"",position:"",fromYear:"",toYear:""}]}))},O=s=>{var n;a.experiences.length>1&&(N(o=>({...o,experiences:o.experiences.filter((p,r)=>r!==s)})),(n=g.experienceYears)!=null&&n[s]&&u(o=>{const p={...o.experienceYears};delete p[s];const r={};return Object.keys(p).forEach(c=>{const m=parseInt(c);m>s?r[m-1]=p[c]:m<s&&(r[m]=p[c])}),{...o,experienceYears:r}}))},j=async()=>{u({description:"",experiences:"",minTrainingCost:"",experienceYears:{}}),F("");const s={};let n=!1;if(a.minTrainingCost||(s.minTrainingCost="Minimum training cost is required",n=!0),a.description.trim()||(s.description="Description is required",n=!0),(a.experiences.length===0||!a.experiences[0].schoolName)&&(s.experiences="At least one experience with school name is required",n=!0),n){u(s),(s.description||s.experiences)&&S(1);return}try{let o=a.profilePic;if(a.selectedImageFile){t(r=>({...r,isUploadingImage:!0,uploadProgress:"Uploading profile image..."})),console.log("Uploading selected image:",a.selectedImageFile.name);try{o=(await d(K(a.selectedImageFile)).unwrap()).data.fileUrl,console.log("Image uploaded successfully:",o),t(c=>({...c,isUploadingImage:!1,uploadProgress:"Image uploaded successfully!"}))}catch(r){console.error("Image upload failed:",r),t(m=>({...m,isUploadingImage:!1,uploadProgress:""}));let c="Failed to upload profile image. Please try again.";r.message?c=r.message:r.errors&&Array.isArray(r.errors)&&(c=r.errors.map(m=>m.msg).join(", ")),u(m=>({...m,profileImage:c})),$(c);return}}t(r=>({...r,isSubmittingForm:!0,uploadProgress:"Submitting onboarding data..."}));const p={description:a.description,profilePic:o,experiences:a.experiences.map(r=>({schoolName:r.schoolName,position:r.position,fromYear:parseInt(r.fromYear)||new Date().getFullYear(),toYear:parseInt(r.toYear)||new Date().getFullYear()})),minTrainingCost:parseFloat(a.minTrainingCost),socialLinks:a.socialLinks,sports:a.sports.length>0?a.sports:["General Sports"],expertise:a.expertise.length>0?a.expertise:["General Training"],certifications:a.certifications};console.log("Submitting onboarding data:",p),d(Q(p))}catch(o){console.error("Submission process failed:",o),t(p=>({...p,isUploadingImage:!1,isSubmittingForm:!1,uploadProgress:""})),$("An error occurred during submission. Please try again.")}};return e.jsx("div",{className:"seller-onboarding-wrapper max-container",children:y===1?e.jsx(J,{formData:a,onInputChange:f,onExperienceChange:M,onAddExperience:L,onRemoveExperience:O,onNext:A,fieldErrors:g}):e.jsxs("div",{className:"seller-onboarding-step2-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step complete",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step active",children:"2"})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Minimum Customer Training Cost"}),e.jsx("input",{type:"number",className:`input min-cost-input ${g.minTrainingCost?"error":""}`,placeholder:"Enter amount",value:a.minTrainingCost,onChange:s=>f("minTrainingCost",s.target.value)}),g.minTrainingCost&&e.jsx("div",{className:"field-error",children:g.minTrainingCost})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Social Media Account"}),e.jsxs("div",{className:"social-inputs-grid",children:[e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon facebook",children:e.jsx(X,{})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Facebook URL",value:a.socialLinks.facebook,onChange:s=>I("facebook",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon linkedin",children:e.jsx(V,{})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Instagram URL",value:a.socialLinks.instagram,onChange:s=>I("instagram",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon twitter",children:e.jsx(H,{})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Twitter URL",value:a.socialLinks.twitter,onChange:s=>I("twitter",s.target.value)})]})]})]}),T&&e.jsxs("div",{className:"server-error-message",children:[e.jsx("div",{className:"error-icon",children:"⚠️"}),e.jsx("div",{className:"error-text",children:T})]}),e.jsxs("div",{className:"next-btn-row",children:[e.jsx("button",{className:"btn btn-outline",onClick:()=>S(1),disabled:l.isUploadingImage||l.isSubmittingForm||v,children:"Back"}),e.jsx("button",{className:"btn btn-primary next-btn",onClick:j,disabled:l.isUploadingImage||l.isSubmittingForm||v,children:l.isUploadingImage||l.isSubmittingForm||v?l.uploadProgress||"Processing...":"Complete Onboarding"})]})]})})};export{ie as default};
