.BuyerAccount {
  display: flex;
  min-height: calc(100vh - 90px);
  padding: var(--heading6) 0;

}

.BuyerAccount .container {
  display: grid;
  grid-template-columns: 25% 1fr;
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--basefont);
  gap: var(--heading6);
}

.BuyerAccount .sidebar {
  flex: 0 0 250px;
  position: sticky;
  top: 90px;
  height: calc(100vh - 120px);
}

.BuyerAccount .content {
  flex: 1;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--heading6);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .BuyerAccount .container {
    max-width: 100%;
    padding: 0 var(--smallfont);
  }

  .BuyerAccount .sidebar {
    flex: 0 0 220px;
  }
}

@media (max-width: 768px) {
  .BuyerAccount .container {
    grid-template-columns: 1fr;
  }

  .BuyerAccount .sidebar {
    display: none;
  }

  .BuyerAccount .content {
    padding: var(--basefont);
    width: 100%;
    overflow-x: scroll;
  }
}
