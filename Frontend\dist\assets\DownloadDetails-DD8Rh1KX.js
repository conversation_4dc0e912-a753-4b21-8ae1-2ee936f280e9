import{ao as n,u as t,e as d,$ as c,j as a,ap as r,Q as m}from"./index-CbuIrTWR.js";import{B as o}from"./BuyerAccount-CrcfuMXo.js";import{m as D}from"./mastercard-logo-C3oSyKKA.js";const h=(l,i)=>{const e=i.find(s=>s.id===l);return e?{...e,orderId:`#${l}245578`,date:e.downloadDate||"30 May 2024",time:"4:30PM",items:1,totalAmount:"$22.00",customer:{name:"<PERSON>",email:"<EMAIL>",phone:"************"},payment:{method:"Mastercard",cardNumber:"**** **** **** 1234"},videoUrl:"https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=800&h=450&auto=format&fit=crop",description:"Learn Vortex - Drills and Coaching Philosophies to Developing Toughness in your Players to Win on the Court and in Life. Coach Vortex has been coaching for over 20 years and some advice for High School Coaches. Coach Vortex speaks on what does the building the Culture/Mindset that his Teams and Coaches for. Coach Vortex also does advice for College Coaches, where knowing him as favorite Drills when help teach a Game speed for toughness."}:null},w=()=>{const{id:l}=n(),i=t(),e=d(c),s=h(l,e);return s?a.jsx("div",{className:"BuyerAccount",children:a.jsxs("div",{className:"container max-container",children:[a.jsx("div",{className:"sidebar",children:a.jsx(o,{})}),a.jsx("div",{className:"content",children:a.jsxs("div",{className:"DownloadDetails",children:[a.jsxs("div",{className:"DownloadDetails__header",children:[a.jsxs("button",{className:"DownloadDetails__back-btn",onClick:()=>i(-1),children:[a.jsx(r,{}),"Back"]}),a.jsxs("h1",{className:"DownloadDetails__title",children:["Details of Order ",s.orderId]})]}),a.jsxs("div",{className:"DownloadDetails__content-info",children:[a.jsx("div",{className:"DownloadDetails__content-image",children:a.jsx("img",{src:"https://images.unsplash.com/photo-*************-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:s.title})}),a.jsxs("div",{className:"DownloadDetails__content-details",children:[a.jsx("h3",{className:"DownloadDetails__content-title",children:s.title}),a.jsxs("p",{className:"DownloadDetails__content-coach",children:["By ",s.coach]})]})]}),a.jsxs("div",{className:"DownloadDetails__order-info",children:[a.jsx("h3",{className:"DownloadDetails__section-title",children:"Order Information"}),a.jsxs("div",{className:"DownloadDetails__info-grid",children:[a.jsxs("div",{className:"DownloadDetails__info-item",children:[a.jsx("span",{className:"DownloadDetails__info-label",children:"Order ID"}),a.jsx("span",{className:"DownloadDetails__info-value",children:s.orderId})]}),a.jsxs("div",{className:"DownloadDetails__info-item",children:[a.jsx("span",{className:"DownloadDetails__info-label",children:"Items"}),a.jsx("span",{className:"DownloadDetails__info-value",children:s.items})]}),a.jsxs("div",{className:"DownloadDetails__info-item",children:[a.jsx("span",{className:"DownloadDetails__info-label",children:"Date"}),a.jsxs("span",{className:"DownloadDetails__info-value",children:[s.date," | ",s.time]})]}),a.jsxs("div",{className:"DownloadDetails__info-item",children:[a.jsx("span",{className:"DownloadDetails__info-label",children:"Total Amount"}),a.jsx("span",{className:"DownloadDetails__info-value",children:s.totalAmount})]})]})]}),a.jsxs("div",{className:"DownloadDetails__details-grid",children:[a.jsxs("div",{className:"DownloadDetails__customer-details",children:[a.jsx("h3",{className:"DownloadDetails__section-title",children:"Customer Details"}),a.jsxs("div",{className:"DownloadDetails__details-content",children:[a.jsxs("div",{className:"DownloadDetails__detail-item",children:[a.jsx("span",{className:"DownloadDetails__detail-label",children:"Name"}),a.jsx("span",{className:"DownloadDetails__detail-value",children:s.customer.name})]}),a.jsxs("div",{className:"DownloadDetails__detail-item",children:[a.jsx("span",{className:"DownloadDetails__detail-label",children:"Email Address"}),a.jsx("span",{className:"DownloadDetails__detail-value",children:s.customer.email})]}),a.jsxs("div",{className:"DownloadDetails__detail-item",children:[a.jsx("span",{className:"DownloadDetails__detail-label",children:"Phone Number"}),a.jsx("span",{className:"DownloadDetails__detail-value",children:s.customer.phone})]})]})]}),a.jsxs("div",{className:"DownloadDetails__payment-details",children:[a.jsx("h3",{className:"DownloadDetails__section-title",children:"Payment Details"}),a.jsx("div",{className:"DownloadDetails__details-content",children:a.jsxs("div",{className:"DownloadDetails__payment-method",children:[a.jsx("img",{src:D,alt:"Mastercard",className:"DownloadDetails__payment-icon"}),a.jsx("span",{className:"DownloadDetails__payment-text",children:s.payment.cardNumber})]})})]})]}),a.jsxs("div",{className:"DownloadDetails__video-section",children:[a.jsx("h3",{className:"DownloadDetails__section-title",children:"Video/Document Info"}),a.jsx("div",{className:"DownloadDetails__video-container",children:a.jsxs("div",{className:"DownloadDetails__video-player",children:[a.jsx("img",{src:s.videoUrl,alt:"Video thumbnail",className:"DownloadDetails__video-thumbnail"}),a.jsx("div",{className:"DownloadDetails__play-overlay",children:a.jsx("button",{className:"DownloadDetails__play-btn",children:a.jsx(m,{})})}),a.jsx("div",{className:"DownloadDetails__video-title-overlay",children:a.jsx("h4",{className:"DownloadDetails__video-title",children:s.title})})]})})]}),a.jsxs("div",{className:"DownloadDetails__description-section",children:[a.jsx("h3",{className:"DownloadDetails__section-title",children:"Description"}),a.jsx("p",{className:"DownloadDetails__description-text",children:s.description})]})]})})]})}):a.jsx("div",{className:"BuyerAccount",children:a.jsxs("div",{className:"container max-container",children:[a.jsx("div",{className:"sidebar",children:a.jsx(o,{})}),a.jsx("div",{className:"content",children:a.jsx("div",{className:"DownloadDetails__error",children:a.jsx("p",{children:"Download not found"})})})]})})};export{w as default};
