import{d,u as l,e as o,ai as n,j as a,ak as t,V as u,K as b,a2 as _,a4 as y,aa as h,aq as m,aj as j,ar as x}from"./index-CbuIrTWR.js";const S=()=>{const i=d(),e=l(),r=o(n),s=c=>{switch(i(j(c)),c){case"dashboard":e("/buyer/account/dashboard");break;case"profile":e("/buyer/account/profile");break;case"downloads":e("/buyer/account/downloads");break;case"requests":e("/buyer/account/requests");break;case"bids":e("/buyer/account/bids");break;case"cards":e("/buyer/account/cards");break;case"logout":i(x()),e("/");break;default:e("/buyer/account/dashboard")}};return a.jsx("div",{className:"BuyerSidebar",children:a.jsx("div",{className:"BuyerSidebar__container",children:a.jsxs("ul",{className:"BuyerSidebar__menu",children:[a.jsxs("li",{className:`BuyerSidebar__item ${r==="dashboard"?"active":""}`,onClick:()=>s("dashboard"),children:[a.jsx(t,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"Dashboard"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="profile"?"active":""}`,onClick:()=>s("profile"),children:[a.jsx(u,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Profile"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="downloads"?"active":""}`,onClick:()=>s("downloads"),children:[a.jsx(b,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Downloads"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="requests"?"active":""}`,onClick:()=>s("requests"),children:[a.jsx(_,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Requests"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="bids"?"active":""}`,onClick:()=>s("bids"),children:[a.jsx(y,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Bids"})]}),a.jsxs("li",{className:`BuyerSidebar__item ${r==="cards"?"active":""}`,onClick:()=>s("cards"),children:[a.jsx(h,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"My Cards"})]}),a.jsxs("li",{className:"BuyerSidebar__item BuyerSidebar__logout",onClick:()=>s("logout"),children:[a.jsx(m,{className:"BuyerSidebar__icon"}),a.jsx("span",{children:"Logout"})]})]})})})};export{S as B};
