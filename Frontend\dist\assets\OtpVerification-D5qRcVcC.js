import{u as K,o as W,d as Y,e as F,r as a,j as n,L as G,f as V,s as H,v as J,h as M}from"./index-CbuIrTWR.js";import{t as i}from"./toast-5PAXudRI.js";const ee=()=>{var E,k,L,I,C;const g=K(),d=W(),v=Y(),{isLoading:u,isError:Q,isSuccess:U,error:X}=F(s=>s.auth),[f,y]=a.useState(["","","","","",""]),r=a.useRef([]),[w,l]=a.useState(""),[p,O]=a.useState(0),[m,P]=a.useState(!0),[b,S]=a.useState(null),h=(E=d.state)==null?void 0:E.userId,N=((k=d.state)==null?void 0:k.phoneNumber)||"+91 (*************",D=((L=d.state)==null?void 0:<PERSON>.cooldownSeconds)||60,R=((I=d.state)==null?void 0:I.isLogin)||!1,j=(C=d.state)==null?void 0:C.developmentOtp;a.useEffect(()=>{r.current[0]&&r.current[0].focus(),h||(i.error("Invalid access. Please try again."),g(R?"/auth":"/signup")),D>0&&(O(D),P(!1))},[]),a.useEffect(()=>{let s;return p>0?s=setTimeout(()=>{O(p-1)},1e3):p===0&&!m&&P(!0),()=>clearTimeout(s)},[p,m]),a.useEffect(()=>{j&&S(j)},[h,j]);const $=(s,e)=>{const t=s.target.value;if(!/^\d*$/.test(t))return;const c=[...f];if(c[e]=t.slice(-1),y(c),w&&l(""),t&&e<5&&r.current[e+1].focus(),t&&e===5){const o=[...c];o[e]=t.slice(-1),o.every(T=>T!=="")&&setTimeout(()=>{x(o)},100)}},q=(s,e)=>{if(s.key==="Backspace"&&!f[e]&&e>0){const t=[...f];t[e-1]="",y(t),r.current[e-1].focus()}s.key==="ArrowLeft"&&e>0&&r.current[e-1].focus(),s.key==="ArrowRight"&&e<5&&r.current[e+1].focus()},A=s=>{s.preventDefault();const e=s.clipboardData.getData("text").trim();if(/^\d{6}$/.test(e)){const t=e.split("");y(t),r.current[5].focus(),setTimeout(()=>{x(t)},100)}},B=async()=>{var s;if(!(!m||u)){y(["","","","","",""]),l(""),v(V()),r.current[0].focus();try{let e;h&&!R?e={userId:h}:e={mobile:N.replace(/\s+/g,"").replace(/[()]/g,"")};const t=await v(H(e)).unwrap();i.otp.success("OTP resent successfully!");const c=t.cooldownSeconds||60;O(c),P(!1),t.developmentOtp&&S(t.developmentOtp)}catch(e){if(e.includes("wait")&&e.includes("seconds")){const t=((s=e.match(/\d+/))==null?void 0:s[0])||60;i.otp.cooldown(parseInt(t)),O(parseInt(t)),P(!1)}else i.api.error({response:{data:{message:e}}})}}},x=async(s=null)=>{var c;const e=s||f;if(e.some(o=>o==="")){l("Please enter the complete OTP");return}if(u)return;const t=e.join("");l(""),v(V());try{const o=await v(J({userId:h,otp:t})).unwrap();i.otp.verificationSuccess();const T=((c=o.user)==null?void 0:c.role)||"buyer";if(T==="seller"){const z=M(o.user);g(z)}else g(T==="admin"?"/admin/dashboard":"/buyer/dashboard")}catch(o){o.includes("Invalid OTP")?(l("Invalid OTP. Please try again."),i.otp.verificationError()):o.includes("expired")?(l("OTP has expired. Please request a new one."),i.error("OTP has expired. Please request a new one.")):(l("Verification failed. Please try again."),i.api.error({response:{data:{message:o}}}))}};return n.jsx("div",{className:"otp-page otp-container",children:n.jsxs("div",{className:"otp-form-container",children:[n.jsx("h1",{className:"otp-title",children:"OTP Verification"}),n.jsx("p",{className:"otp-instruction",children:b?"Enter The OTP Displayed Below":`Enter The OTP Sent To ${N}`}),b&&n.jsxs("div",{className:"development-otp-display",style:{background:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"8px",padding:"12px",margin:"16px 0",textAlign:"center"},children:[n.jsx("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#856404"},children:n.jsx("strong",{children:"Your OTP:"})}),n.jsx("p",{style:{margin:"0",fontSize:"24px",fontWeight:"bold",color:"#856404",letterSpacing:"4px"},children:b})]}),n.jsx("div",{className:"otp-input-group",children:f.map((s,e)=>n.jsx("input",{type:"text",maxLength:1,value:s,onChange:t=>$(t,e),onKeyDown:t=>q(t,e),onPaste:e===0?A:null,ref:t=>r.current[e]=t,className:"otp-input","aria-label":`Digit ${e+1} of OTP`},e))}),w&&n.jsx("p",{className:"otp-error",children:w}),n.jsxs("div",{className:"otp-resend",children:[n.jsx("span",{children:"Didn't Received OTP? "}),n.jsx("button",{onClick:B,className:"resend-button",disabled:!m||u,children:!m&&p>0?`Resend in ${p}s`:u?"Sending...":"Resend"})]}),n.jsx("button",{onClick:x,className:"verify-button btn-primary",disabled:u,children:u?"Verifying...":"Verify"}),n.jsx("div",{className:"back-to-signin",children:n.jsx(G,{to:"/auth",children:"Back To Sign In"})})]})})};export{ee as default};
