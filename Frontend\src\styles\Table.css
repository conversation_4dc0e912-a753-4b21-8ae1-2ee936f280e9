/* Base table styles */
.table-container {
  width: 100%;
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
  overflow: hidden;
  background-color: var(--white);
}

/* Traditional table layout */
.table-container table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  margin: 0;
}

.table-container th,
.table-container td {
  padding: var(--smallfont) var(--extrasmallfont);
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.table-container th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-container tbody tr:hover {
  background-color: var(--primary-light-color);
}

.table-container tbody tr:last-child td {
  border-bottom: none;
}

/* Grid-based table layout */
.table-container.grid .table {
  display: flex;
  flex-direction: column;
}

.table-container.grid .table-header {
  display: grid;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.table-container.grid .table-row {
  display: grid;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.table-container.grid .table-row:last-child {
  border-bottom: none;
}

.table-container.grid .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
}

/* Common styles */
.table-container .empty-message {
  text-align: center;
  padding: var(--heading5);
  color: var(--dark-gray);
  font-style: italic;
}

.table-container .clickable {
  cursor: pointer;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .table-container th,
  .table-container td,
  .table-container.grid .table-cell {
    padding: var(--extrasmallfont);
    font-size: var(--extrasmallfont);
  }
}

/* Tablet and small desktop responsive styles */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
    /* Smooth scrolling on iOS */
    scroll-behavior: smooth;
    /* Add subtle shadow to indicate scrollable content */
    box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, 0.1);
  }

  /* Traditional table layout */
  .table-container table {
    min-width: 700px; /* Increased from 600px for better content visibility */
  }

  /* Grid-based table layout */
  .table-container.grid .table-header,
  .table-container.grid .table-row {
    min-width: 700px; /* Increased from 600px for better content visibility */
  }

  /* Make headers sticky during horizontal scroll */
  .table-container th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bg-gray);
  }

  .table-container.grid .table-header {
    position: sticky;
    top: 0;
    z-index: 10;
  }
}

/* Mobile responsive styles */
@media (max-width: 480px) {
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    /* Enhanced shadow for mobile to better indicate scrollable content */
    box-shadow: inset -15px 0 15px -15px rgba(0, 0, 0, 0.15);
  }

  /* Traditional table layout - mobile optimized */
  .table-container table {
    min-width: 800px; /* Increased for mobile to ensure all content is visible */
  }

  .table-container th,
  .table-container td {
    padding: var(--extrasmallfont) calc(var(--extrasmallfont) / 2);
    font-size: calc(var(--extrasmallfont) * 0.9);
    white-space: nowrap; /* Prevent text wrapping in cells */
  }

  /* Grid-based table layout - mobile optimized */
  .table-container.grid .table-header,
  .table-container.grid .table-row {
    min-width: 800px; /* Increased for mobile */
  }

  .table-container.grid .table-cell {
    padding: calc(var(--extrasmallfont) / 2);
    font-size: calc(var(--extrasmallfont) * 0.9);
    white-space: nowrap;
  }

  /* Enhanced sticky headers for mobile */
  .table-container th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bg-gray);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-container.grid .table-header {
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* Extra small mobile devices */
@media (max-width: 320px) {
  .table-container table {
    min-width: 900px; /* Even larger minimum width for very small screens */
  }

  .table-container.grid .table-header,
  .table-container.grid .table-row {
    min-width: 900px;
  }

  .table-container th,
  .table-container td,
  .table-container.grid .table-cell {
    font-size: calc(var(--extrasmallfont) * 0.85);
  }
}

/* Horizontal scroll indicators for better UX */
.table-container::before,
.table-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 5;
  transition: opacity 0.3s ease;
}

.table-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.8), transparent);
  opacity: 0;
}

.table-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
  opacity: 1;
}

/* Show scroll indicators only on small screens */
@media (max-width: 768px) {
  .table-container {
    position: relative;
  }

  .table-container::after {
    opacity: 1;
  }
}

/* Custom scrollbar styling for webkit browsers */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: var(--light-gray);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* Custom variants can be added here when needed */
