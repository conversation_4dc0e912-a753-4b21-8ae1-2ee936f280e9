import{j as s}from"./index-CbuIrTWR.js";import{m as a}from"./mastercard-logo-C3oSyKKA.js";import{i}from"./herosideimg-D1S8C94Z.js";import{t as e}from"./thankyou-zEUM9FAJ.js";const c=()=>s.jsxs("div",{className:"bid-thank-you-page max-container",children:[s.jsxs("div",{className:"bid-thank-you-header",children:[s.jsx("img",{src:e,alt:"Success",className:"bid-thank-you-header-icon"}),s.jsx("h1",{className:"bid-thank-you-title",children:"Your bid is submitted successfully!"}),s.jsx("p",{className:"bid-thank-you-subtitle",children:"We will update you for the bid status soon via Email or SMS."})]}),s.jsxs("div",{className:"bid-thank-you-card",children:[s.jsxs("div",{className:"bid-thank-you-section bid-information-section",children:[s.jsx("h2",{className:"bid-thank-you-section-title",children:"Bid Information"}),s.jsxs("div",{className:"bid-thank-you-grid bid-info-grid",children:[s.jsxs("div",{className:"bid-thank-you-grid-item",children:[s.jsx("span",{className:"bid-thank-you-label",children:"Bid Id"}),s.jsx("span",{className:"bid-thank-you-value",children:"#12345678"})]}),s.jsxs("div",{className:"bid-thank-you-grid-item",children:[s.jsx("span",{className:"bid-thank-you-label",children:"Price"}),s.jsx("span",{className:"bid-thank-you-value",children:"$22.00"})]}),s.jsxs("div",{className:"bid-thank-you-grid-item",children:[s.jsx("span",{className:"bid-thank-you-label",children:"Date"}),s.jsx("span",{className:"bid-thank-you-value",children:"20 May 2025 | 4:50PM"})]}),s.jsxs("div",{className:"bid-thank-you-grid-item",children:[s.jsx("span",{className:"bid-thank-you-label",children:"Bid Amount"}),s.jsx("span",{className:"bid-thank-you-value",children:"$19.00"})]}),s.jsx("div",{className:"bid-info-divider"})]})]}),s.jsx("hr",{className:"bid-thank-you-divider"}),s.jsx("div",{className:"bid-thank-you-section customer-payment-section",children:s.jsxs("div",{className:"customer-payment-flex-container",children:[s.jsxs("div",{className:"customer-details-container",children:[s.jsx("h2",{className:"bid-thank-you-section-title",children:"Customer Details"}),s.jsxs("div",{className:"bid-thank-you-customer-details",children:[s.jsxs("div",{children:[s.jsx("span",{className:"bid-thank-you-label",children:"Name"}),s.jsx("span",{className:"bid-thank-you-value",children:"John Smith"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"bid-thank-you-label",children:"Email Address"}),s.jsx("span",{className:"bid-thank-you-value",children:"<EMAIL>"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"bid-thank-you-label",children:"Phone Number"}),s.jsx("span",{className:"bid-thank-you-value",children:"************"})]})]})]}),s.jsx("div",{className:"customer-payment-divider"}),s.jsxs("div",{className:"payment-details-container",children:[s.jsx("h2",{className:"bid-thank-you-section-title",children:"Payment Details"}),s.jsxs("div",{className:"bid-thank-you-payment-details",children:[s.jsx("img",{src:a,alt:"Mastercard",className:"bid-thank-you-payment-icon"}),s.jsx("span",{className:"bid-thank-you-value",children:"**** **** **** 1234"})]})]})]})}),s.jsx("hr",{className:"bid-thank-you-divider"}),s.jsxs("div",{className:"bid-thank-you-section",children:[s.jsx("h2",{className:"bid-thank-you-section-title",children:"Item Info"}),s.jsxs("div",{className:"bid-thank-you-item-info",children:[s.jsx("img",{src:i,alt:"Item",className:"bid-thank-you-item-image"}),s.jsxs("div",{className:"bid-thank-you-item-details",children:[s.jsx("p",{className:"bid-thank-you-item-title",children:"Frank Martin - Drills and Coaching Philosophies to Developing Toughness In Your Players"}),s.jsx("p",{className:"bid-thank-you-item-author",children:"By Basketball Coaches Clinic"})]})]})]})]}),s.jsxs("div",{className:"bid-thank-you-actions",children:[s.jsx("button",{type:"button",className:"btn btn-outline bid-thank-you-btn-outline",children:"Go To My Bid Page"}),s.jsx("button",{type:"button",className:"btn btn-primary bid-thank-you-btn-primary",children:"Go To Homepage"})]})]});export{c as default};
