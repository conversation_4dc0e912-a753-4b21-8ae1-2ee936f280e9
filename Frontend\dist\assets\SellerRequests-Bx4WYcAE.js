import{e as i,aw as m,u as d,j as s}from"./index-CbuIrTWR.js";import{S as u}from"./SellerLayout-CjHDmTwH.js";import{a as x}from"./index-DKb6biYd.js";import{T as j}from"./Table-CPAiTSiF.js";import{S as p}from"./index-B_Bht8JV.js";const f=()=>{const t=i(m),l=d(),o=e=>{l(`/seller/request-details/${e.replace("#","")}`)},r=[{key:"no",label:"No.",className:"no"},{key:"id",label:"Request Id"},{key:"content",label:"Videos/Documents",render:e=>s.jsxs("div",{className:"video-doc",children:[s.jsx("img",{src:e.image,alt:e.title}),s.jsx("span",{children:e.title})]})},{key:"date",label:"Date"},{key:"price",label:"Price"},{key:"requestedAmount",label:"Requested Amount"},{key:"requestedCustomer",label:"Requested Customer"},{key:"action",label:"Action",render:e=>s.jsxs("div",{className:"action-icons",children:[s.jsx(p,{className:"eye-icon",onClick:()=>o(e.id)}),s.jsx(x,{className:"comment-icon"})]})}],n=e=>e.map((a,c)=>({...a,no:c+1,date:`${a.date} | 4:50PM`}));return s.jsx(u,{children:s.jsx("div",{className:"seller-requests-container",children:s.jsx(j,{columns:r,data:n(t),className:"requests-table"})})})};export{f as default};
